// Mock auth client for demonstration purposes
// In a real implementation, this would use better-auth
export const authClient = {
  signIn: {
    email: async (credentials: { email: string; password: string }) => {
      // Mock implementation - in real app this would call better-auth
      return {
        data: {
          user: {
            id: '1',
            email: credentials.email,
            name: 'Test User',
            role: 'user',
            status: 'approved',
            createdAt: new Date(),
            updatedAt: new Date(),
            apiAccessLevel: 'basic'
          },
          token: 'mock-token',
          expiresAt: new Date(Date.now() + 3600000)
        },
        error: null
      }
    }
  },
  signUp: {
    email: async (data: { email: string; password: string; name: string }) => {
      // Mock implementation
      return {
        data: {
          user: {
            id: '2',
            email: data.email,
            name: data.name,
            role: 'user',
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date(),
            apiAccessLevel: 'none'
          },
          token: 'mock-token',
          expiresAt: new Date(Date.now() + 3600000)
        },
        error: null
      }
    }
  },
  signOut: async () => {
    // Mock implementation
    return { data: null, error: null }
  },
  getSession: async () => {
    // Mock implementation
    return { data: null, error: null }
  }
}